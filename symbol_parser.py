"""
Symbol parser for extracting options symbols from NSE_FO.csv based on configured underlying symbols.
Filters symbols for current month, next month, and next-next month.
Supports any index or stock symbols configured in YAML with UNDERLYING + YY + MMM + STRIKE + CE/PE pattern.
"""

import csv
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OptionSymbol:
    """Data class to represent an option symbol."""
    symbol: str
    underlying: str
    expiry_date: str
    strike_price: float
    option_type: str  # CE or PE
    full_symbol: str  # NSE:NIFTY25JUL57000CE format

    @property
    def strike(self) -> float:
        """Alias for strike_price for backward compatibility."""
        return self.strike_price

class SymbolParser:
    """Parser for extracting and filtering option symbols from NSE_FO.csv."""
    
    def __init__(self, csv_file_path: str = "NSE_FO.csv", target_symbols: Optional[List[str]] = None):
        """
        Initialize the symbol parser.
        
        Args:
            csv_file_path: Path to the NSE_FO.csv file
            target_symbols: List of underlying symbols to filter (e.g., ['NIFTY', 'BANKNIFTY', 'RELIANCE'])
                          If None, defaults to ['NIFTY', 'BANKNIFTY'] for backward compatibility
        """
        self.csv_file_path = csv_file_path
        if target_symbols is None:
            self.target_symbols = {'NIFTY', 'BANKNIFTY'}
            logger.info("Using default target symbols: NIFTY, BANKNIFTY")
        else:
            self.target_symbols = set(target_symbols)
            logger.info(f"Using configured target symbols: {', '.join(sorted(self.target_symbols))}")
        
        # Create dynamic regex pattern for configured symbols
        self._create_symbol_pattern()
    
    def _create_symbol_pattern(self) -> None:
        """
        Create dynamic regex pattern based on configured target symbols.
        Pattern: UNDERLYING + YY + MMM + STRIKE + CE/PE
        """
        if not self.target_symbols:
            raise ValueError("No target symbols configured")
        
        # Escape special regex characters in symbol names and create alternation
        escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
        symbols_pattern = '|'.join(escaped_symbols)
        
        # Create the complete pattern: (SYMBOL1|SYMBOL2|...)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)
        self.symbol_pattern = rf'^({symbols_pattern})(\d{{2}})([A-Z]{{3}})(\d+(?:\.\d+)?)(CE|PE)$'
        
        logger.debug(f"Created dynamic symbol pattern: {self.symbol_pattern}")
        
    def get_target_months(self) -> List[str]:
        """
        Get the target months for filtering. Always returns 3 months.
        If current date has passed this month's expiry (last Thursday),
        consider next 3 months instead of current month + next 2 months.

        Returns:
            List of month abbreviations in format like ['JUL', 'AUG', 'SEP']
        """
        current_date = datetime.now()

        # Check if current month's expiry has passed
        current_month_expiry = self._get_last_thursday_of_month(current_date.year, current_date.month)

        if current_date.date() > current_month_expiry:
            # Current month expiry has passed, use next 3 months
            logger.info(f"Current month expiry ({current_month_expiry}) has passed. Using next 3 months.")
            start_offset = 1  # Start from next month
        else:
            # Current month expiry hasn't passed, use current month + next 2 months
            logger.info(f"Current month expiry ({current_month_expiry}) hasn't passed. Using current + next 2 months.")
            start_offset = 0  # Start from current month

        months = []
        for i in range(3):  # Always get exactly 3 months
            target_date = current_date + timedelta(days=30 * (start_offset + i))
            month_abbr = target_date.strftime('%b').upper()
            months.append(month_abbr)

        # Remove duplicates while preserving order
        unique_months = []
        for month in months:
            if month not in unique_months:
                unique_months.append(month)

        # Ensure we always have exactly 3 months
        while len(unique_months) < 3:
            # Add additional months if needed due to duplicate removal
            additional_offset = start_offset + len(months)
            target_date = current_date + timedelta(days=30 * additional_offset)
            month_abbr = target_date.strftime('%b').upper()
            if month_abbr not in unique_months:
                unique_months.append(month_abbr)
            months.append(month_abbr)

        # Take only the first 3 unique months
        unique_months = unique_months[:3]

        logger.info(f"Target months for filtering: {unique_months}")
        return unique_months

    def _get_last_thursday_of_month(self, year: int, month: int):
        """
        Get the last Thursday of a given month (NIFTY/BANKNIFTY expiry date).

        Args:
            year: Year
            month: Month (1-12)

        Returns:
            date: Last Thursday of the month
        """
        from datetime import date, timedelta

        # Get the last day of the month
        if month == 12:
            last_day = date(year, month, 31)
        else:
            last_day = date(year, month + 1, 1) - timedelta(days=1)

        # Get the last Thursday of the month
        # Thursday is weekday 3 (Monday=0, Tuesday=1, Wednesday=2, Thursday=3, ...)
        offset = (last_day.weekday() - 3) % 7
        last_thursday = last_day - timedelta(days=offset)
        return last_thursday
    
    def parse_symbol_from_nse_format(self, nse_symbol: str) -> Optional[OptionSymbol]:
        """
        Parse NSE format symbol like 'NSE:NIFTY25JUL57000CE' into components.
        Only accepts symbols matching the pattern: UNDERLYING + YY + MMM + STRIKE + CE/PE
        
        Args:
            nse_symbol: Symbol in NSE format
            
        Returns:
            OptionSymbol object or None if parsing fails or doesn't match the required pattern
        """
        try:
            # Remove NSE: prefix if present
            symbol = nse_symbol.replace('NSE:', '')
            
            # Use the dynamic pattern created for configured symbols
            # Pattern matches: UNDERLYING + YY + MMM + STRIKE + CE/PE
            # Example: NIFTY25JUL57000CE, BANKNIFTY25SEP25500PE, RELIANCE25JUL2500CE
            match = re.match(self.symbol_pattern, symbol)
            
            if not match:
                # Symbol doesn't match the required pattern or underlying not in target symbols
                return None
                
            underlying = match.group(1)
            year = match.group(2)
            month = match.group(3)
            strike = float(match.group(4))
            option_type = match.group(5)
            
            # Verify underlying is in our target symbols (double-check)
            if underlying not in self.target_symbols:
                return None
            
            # Create expiry date string
            expiry_date = f"{year}{month}"
            
            return OptionSymbol(
                symbol=symbol,
                underlying=underlying,
                expiry_date=expiry_date,
                strike_price=strike,
                option_type=option_type,
                full_symbol=nse_symbol
            )
            
        except Exception as e:
            logger.debug(f"Failed to parse symbol {nse_symbol}: {e}")
            return None
    
    def load_symbols_from_csv(self) -> List[OptionSymbol]:
        """
        Load and parse symbols from NSE_FO.csv file.
        
        Returns:
            List of OptionSymbol objects
        """
        symbols = []
        target_months = self.get_target_months()
        
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                
                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()
                            
                            # Parse the symbol
                            parsed_symbol = self.parse_symbol_from_nse_format(nse_symbol)
                            
                            if parsed_symbol:
                                # Filter for target symbols (NIFTY, BANKNIFTY)
                                if parsed_symbol.underlying in self.target_symbols:
                                    # Extract month from expiry date (last 3 characters)
                                    month_part = parsed_symbol.expiry_date[-3:]
                                    
                                    # Filter for target months
                                    if month_part in target_months:
                                        symbols.append(parsed_symbol)
                                        
                    except Exception as e:
                        logger.debug(f"Error processing row {row_num}: {e}")
                        continue
                        
        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file_path}")
            raise
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            raise
            
        logger.info(f"Loaded {len(symbols)} symbols from CSV for target months")
        return symbols
    
    def filter_symbols_by_underlying(self, symbols: List[OptionSymbol], 
                                   underlying_symbols: List[str]) -> List[OptionSymbol]:
        """
        Filter symbols by underlying symbols.
        
        Args:
            symbols: List of OptionSymbol objects
            underlying_symbols: List of underlying symbols to filter by
            
        Returns:
            Filtered list of OptionSymbol objects
        """
        filtered = [s for s in symbols if s.underlying in underlying_symbols]
        logger.info(f"Filtered to {len(filtered)} symbols for underlyings: {underlying_symbols}")
        return filtered
    
    def get_symbols_for_scanning(self, underlying_symbols: List[str] = None) -> List[str]:
        """
        Get list of symbols ready for market data scanning.
        
        Args:
            underlying_symbols: List of underlying symbols to filter by (default: NIFTY, BANKNIFTY)
            
        Returns:
            List of symbol strings in NSE format
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)
            
        # Load symbols from CSV
        all_symbols = self.load_symbols_from_csv()
        
        # Filter by underlying symbols
        filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)
        
        # Extract full symbol strings
        symbol_strings = [s.full_symbol for s in filtered_symbols]
        
        logger.info(f"Prepared {len(symbol_strings)} symbols for scanning")
        return symbol_strings
    
    def get_symbol_details(self, underlying_symbols: List[str] = None) -> List[OptionSymbol]:
        """
        Get detailed symbol information for analysis.

        Args:
            underlying_symbols: List of underlying symbols to filter by

        Returns:
            List of OptionSymbol objects with detailed information
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)

        # Load symbols from CSV
        all_symbols = self.load_symbols_from_csv()

        # Filter by underlying symbols
        filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)

        return filtered_symbols

    def save_filtered_symbols_to_csv(self, symbols: List[OptionSymbol], filename: str = "filtered_symbols.csv") -> bool:
        """
        Save filtered symbols to CSV file to avoid rate limiting issues.

        Args:
            symbols: List of OptionSymbol objects to save
            filename: Name of the CSV file to save

        Returns:
            True if successful, False otherwise
        """
        try:
            import csv
            import os

            # Create reports directory if it doesn't exist
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            filepath = os.path.join(reports_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['symbol', 'underlying', 'expiry_date', 'strike_price', 'option_type', 'full_symbol']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for symbol in symbols:
                    writer.writerow({
                        'symbol': symbol.symbol,
                        'underlying': symbol.underlying,
                        'expiry_date': symbol.expiry_date,
                        'strike_price': symbol.strike_price,
                        'option_type': symbol.option_type,
                        'full_symbol': symbol.full_symbol
                    })

            logger.info(f"Saved {len(symbols)} filtered symbols to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving filtered symbols to CSV: {e}")
            return False
